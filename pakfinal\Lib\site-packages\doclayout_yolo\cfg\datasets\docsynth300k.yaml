# Ultralytics YOLO 🚀, AGPL-3.0 license
# COCO 2017 dataset https://cocodataset.org by Microsoft
# Documentation: https://docs.doclayout_yolo.com/datasets/detect/coco/
# Example usage: yolo train data=coco.yaml
# parent
# ├── doclayout_yolo
# └── datasets
#     └── coco  ← downloads here (20.1 GB)

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: ./layout_data/docsynth300k # dataset root dir
train: train300k.txt # train images (relative to 'path') 118287 images
val: val.txt # val images (relative to 'path') 5000 images
test: val.txt # 20288 of 40670 images, submit to https://competitions.codalab.org/competitions/20794

# Classes
names:
  0: 'QR code'
  1: 'advertisement'
  2: 'algorithm'
  3: 'answer'
  4: 'author'
  5: 'barcode'
  6: 'bill'
  7: 'blank'
  8: 'bracket'
  9: 'breakout'
  10: 'byline'
  11: 'caption'
  12: 'catalogue'
  13: 'chapter title'
  14: 'code'
  15: 'correction'
  16: 'credit'
  17: 'dateline'
  18: 'drop cap'
  19: "editor's note"
  20: 'endnote'
  21: 'examinee information'
  22: 'fifth-level title'
  23: 'figure'
  24: 'first-level question number'
  25: 'first-level title'
  26: 'flag'
  27: 'folio'
  28: 'footer'
  29: 'footnote'
  30: 'formula'
  31: 'fourth-level section title'
  32: 'fourth-level title'
  33: 'header'
  34: 'headline'
  35: 'index'
  36: 'inside'
  37: 'institute'
  38: 'jump line'
  39: 'kicker'
  40: 'lead'
  41: 'marginal note'
  42: 'matching'
  43: 'mugshot'
  44: 'option'
  45: 'ordered list'
  46: 'other question number'
  47: 'page number'
  48: 'paragraph'
  49: 'part'
  50: 'play'
  51: 'poem'
  52: 'reference'
  53: 'sealing line'
  54: 'second-level question number'
  55: 'second-level title'
  56: 'section'
  57: 'section title'
  58: 'sidebar'
  59: 'sub section title'
  60: 'subhead'
  61: 'subsub section title'
  62: 'supplementary note'
  63: 'table'
  64: 'table caption'
  65: 'table note'
  66: 'teasers'
  67: 'third-level question number'
  68: 'third-level title'
  69: 'title'
  70: 'translator'
  71: 'underscore'
  72: 'unordered list'
  73: 'weather forecast'