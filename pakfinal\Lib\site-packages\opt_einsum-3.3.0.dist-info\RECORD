opt_einsum-3.3.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
opt_einsum-3.3.0.dist-info/LICENSE,sha256=eyZpGnyO5Ir0JaU8uuqryao6hOouk_5HoezFUMHedts,1080
opt_einsum-3.3.0.dist-info/METADATA,sha256=BLffQEqQGbUAW_KMg6nZUWpo-q9wrM0CLIBbecG38qM,6536
opt_einsum-3.3.0.dist-info/RECORD,,
opt_einsum-3.3.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opt_einsum-3.3.0.dist-info/WHEEL,sha256=g4nMs7d-Xl9-xC9XovUrsDHGXt-FT0E17Yqo92DEfvY,92
opt_einsum-3.3.0.dist-info/top_level.txt,sha256=slDeGRKw77FSsfCUpC5QLvFOMVhHHHD7MFUxfjy0Tpc,11
opt_einsum-3.3.0.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
opt_einsum/__init__.py,sha256=j9wzXf5oCmD7s93YYdgRXc-nQCLIHZ-V46BKHekFcQw,693
opt_einsum/_version.py,sha256=iymjy1c35NzSXfxE_k2CBcIuKHPlyak7mY1CxUYbjLU,498
opt_einsum/backends/__init__.py,sha256=ARKEviw_HcTWE7f1wD55pRMsLhRDMRbA80fKYT2W-R4,494
opt_einsum/backends/cupy.py,sha256=vzAjh9Fqh1J-Dks3TQWRkEx6T4UNhtsfGJ_sMF_dtCQ,919
opt_einsum/backends/dispatch.py,sha256=iTDRQDWyy3V9FxdCOkgBUTrR9aQCzChWui_HHT9nbdw,4420
opt_einsum/backends/jax.py,sha256=Q_i-Zj-7VrDfXUGK0XKV2a47UjiFQeT3XmY_dTQQ80Q,1044
opt_einsum/backends/object_arrays.py,sha256=V7WnXTX1PTN7OdwpabZ9pdXgl7-CdBXf5CHpkcVBKdU,1928
opt_einsum/backends/tensorflow.py,sha256=mk-lZC4tI9c1JnpQdo7EY_YPCyc5ZoL3hWlPxqzvtns,3857
opt_einsum/backends/theano.py,sha256=jkG-ZX128VsC01L5VZqUk7aoYjE0UVp_gXgn5pGXw8s,1657
opt_einsum/backends/torch.py,sha256=cI6Ey8uRcAWCs8tEN1-K4X3wreYRkmMQxw5OeykD_KA,3506
opt_einsum/blas.py,sha256=giEYf8DB1ion3QI2m4h6IcfCJpwgIJG7zq8NTlW0C5s,7705
opt_einsum/contract.py,sha256=-bW0yXlvFH68sTJDKCn1pJI3Y2oqcejC7j9w_UK4WLI,35278
opt_einsum/helpers.py,sha256=zYrDqQ_v85NDkJti35nJR82j4Z-ibMdvGiqL8O_T2ak,7670
opt_einsum/parser.py,sha256=hj-qrgoVvtLwDUOoeMq8y4VLrNKUWmsMh_TuTk38Isw,10749
opt_einsum/path_random.py,sha256=dRIGdsIFVIO4MzOrWyKZMspw6lrSfWhGyXkC4a5eBzU,13660
opt_einsum/paths.py,sha256=lak_3bF2sLRg7g03jCQpXA5tCAPg5N8QZzo4_mOBtUQ,41998
opt_einsum/sharing.py,sha256=tjmGlC6ggTnFv08naVmQ8ipamKLd0RJqi2XVpkpiMaM,6298
opt_einsum/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opt_einsum/tests/test_backends.py,sha256=hAUPeEdIkkNJWRRxlj-J5yCPOGtdJHETIxlqfo2XcGU,15348
opt_einsum/tests/test_blas.py,sha256=3UK9GXe4gvMB-KEOTcC3rezGAQzL7LfszNAGXGcvmLo,4131
opt_einsum/tests/test_contract.py,sha256=_UXv3zpxdsjfbtISuRr-OWuu45FDxdwSgKwUM2nUUnU,7787
opt_einsum/tests/test_edge_cases.py,sha256=Fb2ZYKIYdOZ-OF9EQ6vBSZHKR9PbmvTP09q5vdcJ8ro,4159
opt_einsum/tests/test_input.py,sha256=2ZFjuWMe4RknzyZRI9LKHy1kBIZjVpV6pmr_qpoprgM,7960
opt_einsum/tests/test_paths.py,sha256=HO9O2ZZMvkE0CICH8UOzLD6Qcf29pW0AsYgP_mePoHw,15744
opt_einsum/tests/test_sharing.py,sha256=xoaNs2cgfds46v56O7H9BTgC9sZNwf-nyTpAXpwPbtY,12655
