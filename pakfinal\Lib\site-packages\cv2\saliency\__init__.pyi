__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Classes
class Saliency(cv2.Algorithm):
    # Functions
    @_typing.overload
    def computeSaliency(self, image: cv2.typing.MatLike, saliencyMap: cv2.typing.MatLike | None = ...) -> tuple[bool, cv2.typing.MatLike]: ...
    @_typing.overload
    def computeSaliency(self, image: cv2.UMat, saliencyMap: cv2.UMat | None = ...) -> tuple[bool, cv2.UMat]: ...


class StaticSaliency(Saliency):
    # Functions
    @_typing.overload
    def computeBinaryMap(self, _saliencyMap: cv2.typing.MatLike, _binaryMap: cv2.typing.MatLike | None = ...) -> tuple[bool, cv2.typing.MatLike]: ...
    @_typing.overload
    def computeBinaryMap(self, _saliencyMap: cv2.UMat, _binaryMap: cv2.UMat | None = ...) -> tuple[bool, cv2.UMat]: ...


class MotionSaliency(Saliency):
    ...

class Objectness(Saliency):
    ...

class StaticSaliencySpectralResidual(StaticSaliency):
    # Functions
    @classmethod
    def create(cls) -> StaticSaliencySpectralResidual: ...

    @_typing.overload
    def computeSaliency(self, image: cv2.typing.MatLike, saliencyMap: cv2.typing.MatLike | None = ...) -> tuple[bool, cv2.typing.MatLike]: ...
    @_typing.overload
    def computeSaliency(self, image: cv2.UMat, saliencyMap: cv2.UMat | None = ...) -> tuple[bool, cv2.UMat]: ...

    def read(self, fn: cv2.FileNode) -> None: ...

    def getImageWidth(self) -> int: ...

    def setImageWidth(self, val: int) -> None: ...

    def getImageHeight(self) -> int: ...

    def setImageHeight(self, val: int) -> None: ...


class StaticSaliencyFineGrained(StaticSaliency):
    # Functions
    @classmethod
    def create(cls) -> StaticSaliencyFineGrained: ...

    @_typing.overload
    def computeSaliency(self, image: cv2.typing.MatLike, saliencyMap: cv2.typing.MatLike | None = ...) -> tuple[bool, cv2.typing.MatLike]: ...
    @_typing.overload
    def computeSaliency(self, image: cv2.UMat, saliencyMap: cv2.UMat | None = ...) -> tuple[bool, cv2.UMat]: ...


class MotionSaliencyBinWangApr2014(MotionSaliency):
    # Functions
    @classmethod
    def create(cls) -> MotionSaliencyBinWangApr2014: ...

    @_typing.overload
    def computeSaliency(self, image: cv2.typing.MatLike, saliencyMap: cv2.typing.MatLike | None = ...) -> tuple[bool, cv2.typing.MatLike]: ...
    @_typing.overload
    def computeSaliency(self, image: cv2.UMat, saliencyMap: cv2.UMat | None = ...) -> tuple[bool, cv2.UMat]: ...

    def setImagesize(self, W: int, H: int) -> None: ...

    def init(self) -> bool: ...

    def getImageWidth(self) -> int: ...

    def setImageWidth(self, val: int) -> None: ...

    def getImageHeight(self) -> int: ...

    def setImageHeight(self, val: int) -> None: ...


class ObjectnessBING(Objectness):
    # Functions
    @classmethod
    def create(cls) -> ObjectnessBING: ...

    @_typing.overload
    def computeSaliency(self, image: cv2.typing.MatLike, saliencyMap: cv2.typing.MatLike | None = ...) -> tuple[bool, cv2.typing.MatLike]: ...
    @_typing.overload
    def computeSaliency(self, image: cv2.UMat, saliencyMap: cv2.UMat | None = ...) -> tuple[bool, cv2.UMat]: ...

    def read(self) -> None: ...

    def write(self) -> None: ...

    def getobjectnessValues(self) -> _typing.Sequence[float]: ...

    def setTrainingPath(self, trainingPath: str) -> None: ...

    def setBBResDir(self, resultsDir: str) -> None: ...

    def getBase(self) -> float: ...

    def setBase(self, val: float) -> None: ...

    def getNSS(self) -> int: ...

    def setNSS(self, val: int) -> None: ...

    def getW(self) -> int: ...

    def setW(self, val: int) -> None: ...



