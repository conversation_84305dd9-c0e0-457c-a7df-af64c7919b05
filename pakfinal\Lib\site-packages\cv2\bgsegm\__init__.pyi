__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Enumerations
LSBP_CAMERA_MOTION_COMPENSATION_NONE: int
LSBP_CAMERA_MOTION_COMPENSATION_LK: int
LSBPCameraMotionCompensation = int
"""One of [LSBP_CAMERA_MOTION_COMPENSATION_NONE, LSBP_CAMERA_MOTION_COMPENSATION_LK]"""



# Classes
class BackgroundSubtractorMOG(cv2.BackgroundSubtractor):
    # Functions
    def getHistory(self) -> int: ...

    def setHistory(self, nframes: int) -> None: ...

    def getNMixtures(self) -> int: ...

    def setNMixtures(self, nmix: int) -> None: ...

    def getBackgroundRatio(self) -> float: ...

    def setBackgroundRatio(self, backgroundRatio: float) -> None: ...

    def getNoiseSigma(self) -> float: ...

    def setNoiseSigma(self, noiseSigma: float) -> None: ...


class BackgroundSubtractorGMG(cv2.BackgroundSubtractor):
    # Functions
    @_typing.overload
    def apply(self, image: cv2.typing.MatLike, fgmask: cv2.typing.MatLike | None = ..., learningRate: float = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, image: cv2.UMat, fgmask: cv2.UMat | None = ..., learningRate: float = ...) -> cv2.UMat: ...

    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.UMat | None = ...) -> cv2.UMat: ...

    def getMaxFeatures(self) -> int: ...

    def setMaxFeatures(self, maxFeatures: int) -> None: ...

    def getDefaultLearningRate(self) -> float: ...

    def setDefaultLearningRate(self, lr: float) -> None: ...

    def getNumFrames(self) -> int: ...

    def setNumFrames(self, nframes: int) -> None: ...

    def getQuantizationLevels(self) -> int: ...

    def setQuantizationLevels(self, nlevels: int) -> None: ...

    def getBackgroundPrior(self) -> float: ...

    def setBackgroundPrior(self, bgprior: float) -> None: ...

    def getSmoothingRadius(self) -> int: ...

    def setSmoothingRadius(self, radius: int) -> None: ...

    def getDecisionThreshold(self) -> float: ...

    def setDecisionThreshold(self, thresh: float) -> None: ...

    def getUpdateBackgroundModel(self) -> bool: ...

    def setUpdateBackgroundModel(self, update: bool) -> None: ...

    def getMinVal(self) -> float: ...

    def setMinVal(self, val: float) -> None: ...

    def getMaxVal(self) -> float: ...

    def setMaxVal(self, val: float) -> None: ...


class BackgroundSubtractorCNT(cv2.BackgroundSubtractor):
    # Functions
    @_typing.overload
    def apply(self, image: cv2.typing.MatLike, fgmask: cv2.typing.MatLike | None = ..., learningRate: float = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, image: cv2.UMat, fgmask: cv2.UMat | None = ..., learningRate: float = ...) -> cv2.UMat: ...

    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.UMat | None = ...) -> cv2.UMat: ...

    def getMinPixelStability(self) -> int: ...

    def setMinPixelStability(self, value: int) -> None: ...

    def getMaxPixelStability(self) -> int: ...

    def setMaxPixelStability(self, value: int) -> None: ...

    def getUseHistory(self) -> bool: ...

    def setUseHistory(self, value: bool) -> None: ...

    def getIsParallel(self) -> bool: ...

    def setIsParallel(self, value: bool) -> None: ...


class BackgroundSubtractorGSOC(cv2.BackgroundSubtractor):
    # Functions
    @_typing.overload
    def apply(self, image: cv2.typing.MatLike, fgmask: cv2.typing.MatLike | None = ..., learningRate: float = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, image: cv2.UMat, fgmask: cv2.UMat | None = ..., learningRate: float = ...) -> cv2.UMat: ...

    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.UMat | None = ...) -> cv2.UMat: ...


class BackgroundSubtractorLSBP(cv2.BackgroundSubtractor):
    # Functions
    @_typing.overload
    def apply(self, image: cv2.typing.MatLike, fgmask: cv2.typing.MatLike | None = ..., learningRate: float = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def apply(self, image: cv2.UMat, fgmask: cv2.UMat | None = ..., learningRate: float = ...) -> cv2.UMat: ...

    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def getBackgroundImage(self, backgroundImage: cv2.UMat | None = ...) -> cv2.UMat: ...


class BackgroundSubtractorLSBPDesc:
    ...

class SyntheticSequenceGenerator(cv2.Algorithm):
    # Functions
    @_typing.overload
    def __init__(self, background: cv2.typing.MatLike, object: cv2.typing.MatLike, amplitude: float, wavelength: float, wavespeed: float, objspeed: float) -> None: ...
    @_typing.overload
    def __init__(self, background: cv2.UMat, object: cv2.UMat, amplitude: float, wavelength: float, wavespeed: float, objspeed: float) -> None: ...

    @_typing.overload
    def getNextFrame(self, frame: cv2.typing.MatLike | None = ..., gtMask: cv2.typing.MatLike | None = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike]: ...
    @_typing.overload
    def getNextFrame(self, frame: cv2.UMat | None = ..., gtMask: cv2.UMat | None = ...) -> tuple[cv2.UMat, cv2.UMat]: ...



# Functions
def createBackgroundSubtractorCNT(minPixelStability: int = ..., useHistory: bool = ..., maxPixelStability: int = ..., isParallel: bool = ...) -> BackgroundSubtractorCNT: ...

def createBackgroundSubtractorGMG(initializationFrames: int = ..., decisionThreshold: float = ...) -> BackgroundSubtractorGMG: ...

def createBackgroundSubtractorGSOC(mc: int = ..., nSamples: int = ..., replaceRate: float = ..., propagationRate: float = ..., hitsThreshold: int = ..., alpha: float = ..., beta: float = ..., blinkingSupressionDecay: float = ..., blinkingSupressionMultiplier: float = ..., noiseRemovalThresholdFacBG: float = ..., noiseRemovalThresholdFacFG: float = ...) -> BackgroundSubtractorGSOC: ...

def createBackgroundSubtractorLSBP(mc: int = ..., nSamples: int = ..., LSBPRadius: int = ..., Tlower: float = ..., Tupper: float = ..., Tinc: float = ..., Tdec: float = ..., Rscale: float = ..., Rincdec: float = ..., noiseRemovalThresholdFacBG: float = ..., noiseRemovalThresholdFacFG: float = ..., LSBPthreshold: int = ..., minCount: int = ...) -> BackgroundSubtractorLSBP: ...

def createBackgroundSubtractorMOG(history: int = ..., nmixtures: int = ..., backgroundRatio: float = ..., noiseSigma: float = ...) -> BackgroundSubtractorMOG: ...

@_typing.overload
def createSyntheticSequenceGenerator(background: cv2.typing.MatLike, object: cv2.typing.MatLike, amplitude: float = ..., wavelength: float = ..., wavespeed: float = ..., objspeed: float = ...) -> SyntheticSequenceGenerator: ...
@_typing.overload
def createSyntheticSequenceGenerator(background: cv2.UMat, object: cv2.UMat, amplitude: float = ..., wavelength: float = ..., wavespeed: float = ..., objspeed: float = ...) -> SyntheticSequenceGenerator: ...


