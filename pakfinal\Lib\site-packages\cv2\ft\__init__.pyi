__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Enumerations
LINEAR: int
SINUS: int
ONE_STEP: int
MULTI_STEP: int
ITERATIVE: int



# Functions
@_typing.overload
def FT02D_FL_process(matrix: cv2.typing.MatLike, radius: int, output: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT02D_FL_process(matrix: cv2.UMat, radius: int, output: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT02D_FL_process_float(matrix: cv2.typing.MatLike, radius: int, output: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT02D_FL_process_float(matrix: cv2.UMat, radius: int, output: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT02D_components(matrix: cv2.typing.MatLike, kernel: cv2.typing.MatLike, components: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT02D_components(matrix: cv2.UMat, kernel: cv2.UMat, components: cv2.UMat | None = ..., mask: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT02D_inverseFT(components: cv2.typing.MatLike, kernel: cv2.typing.MatLike, width: int, height: int, output: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT02D_inverseFT(components: cv2.UMat, kernel: cv2.UMat, width: int, height: int, output: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT02D_iteration(matrix: cv2.typing.MatLike, kernel: cv2.typing.MatLike, mask: cv2.typing.MatLike, firstStop: bool, output: cv2.typing.MatLike | None = ..., maskOutput: cv2.typing.MatLike | None = ...) -> tuple[int, cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def FT02D_iteration(matrix: cv2.UMat, kernel: cv2.UMat, mask: cv2.UMat, firstStop: bool, output: cv2.UMat | None = ..., maskOutput: cv2.UMat | None = ...) -> tuple[int, cv2.UMat, cv2.UMat]: ...

@_typing.overload
def FT02D_process(matrix: cv2.typing.MatLike, kernel: cv2.typing.MatLike, output: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT02D_process(matrix: cv2.UMat, kernel: cv2.UMat, output: cv2.UMat | None = ..., mask: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT12D_components(matrix: cv2.typing.MatLike, kernel: cv2.typing.MatLike, components: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT12D_components(matrix: cv2.UMat, kernel: cv2.UMat, components: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT12D_createPolynomMatrixHorizontal(radius: int, chn: int, matrix: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT12D_createPolynomMatrixHorizontal(radius: int, chn: int, matrix: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT12D_createPolynomMatrixVertical(radius: int, chn: int, matrix: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT12D_createPolynomMatrixVertical(radius: int, chn: int, matrix: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT12D_inverseFT(components: cv2.typing.MatLike, kernel: cv2.typing.MatLike, width: int, height: int, output: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT12D_inverseFT(components: cv2.UMat, kernel: cv2.UMat, width: int, height: int, output: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def FT12D_polynomial(matrix: cv2.typing.MatLike, kernel: cv2.typing.MatLike, c00: cv2.typing.MatLike | None = ..., c10: cv2.typing.MatLike | None = ..., c01: cv2.typing.MatLike | None = ..., components: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ...) -> tuple[cv2.typing.MatLike, cv2.typing.MatLike, cv2.typing.MatLike, cv2.typing.MatLike]: ...
@_typing.overload
def FT12D_polynomial(matrix: cv2.UMat, kernel: cv2.UMat, c00: cv2.UMat | None = ..., c10: cv2.UMat | None = ..., c01: cv2.UMat | None = ..., components: cv2.UMat | None = ..., mask: cv2.UMat | None = ...) -> tuple[cv2.UMat, cv2.UMat, cv2.UMat, cv2.UMat]: ...

@_typing.overload
def FT12D_process(matrix: cv2.typing.MatLike, kernel: cv2.typing.MatLike, output: cv2.typing.MatLike | None = ..., mask: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def FT12D_process(matrix: cv2.UMat, kernel: cv2.UMat, output: cv2.UMat | None = ..., mask: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def createKernel(function: int, radius: int, chn: int, kernel: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def createKernel(function: int, radius: int, chn: int, kernel: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def createKernel1(A: cv2.typing.MatLike, B: cv2.typing.MatLike, chn: int, kernel: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def createKernel1(A: cv2.UMat, B: cv2.UMat, chn: int, kernel: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def filter(image: cv2.typing.MatLike, kernel: cv2.typing.MatLike, output: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def filter(image: cv2.UMat, kernel: cv2.UMat, output: cv2.UMat | None = ...) -> cv2.UMat: ...

@_typing.overload
def inpaint(image: cv2.typing.MatLike, mask: cv2.typing.MatLike, radius: int, function: int, algorithm: int, output: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
@_typing.overload
def inpaint(image: cv2.UMat, mask: cv2.UMat, radius: int, function: int, algorithm: int, output: cv2.UMat | None = ...) -> cv2.UMat: ...


